import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Tuple, Union, Optional, Any
from datetime import datetime
import os
import json
import inspect
import talib
from pyqlab.models.gpt2.candlestick_vq_dataset import CandlestickDataset

# 导入信号生成器模块
try:
    from pyqlab.models.gpt2.signal_generator import (
        SignalGenerator,
        ThresholdDirectionStrategy,
        TopKStrategy,
        StatisticalMomentumStrategy,
        WeightedEnsembleStrategy
    )
except ImportError:
    # 如果模块不存在，创建一个空的标记
    SignalGenerator = None

# 尝试导入模型类，用于类型检查
try:
    from pyqlab.models.gpt2.candlestick_vq_gpt import CandlestickVQGPT
    from pyqlab.models.gpt2.bak.candlestick_gpt4 import CandlestickGPT4
    MODELS_IMPORTED = True
except ImportError:
    MODELS_IMPORTED = False

signal_configs = {
        "传统策略": None,  # 不使用信号生成器

        "threshold": {
            "type": "threshold",
            "params": {
                "threshold": 0.5  # 较低的阈值
            }
        },

        "topk": {
            "type": "topk",
            "params": {
                "k": 5,
                "min_prob": 0.2
            }
        },

        "momentum": {
            "type": "momentum",
            "params": {
                "momentum_threshold": 0.15,
                "min_prob": 0.3
            }
        },

        "ensemble": {
            "type": "ensemble",
            "params": {
                "strategies": [
                    {
                        "type": "threshold",
                        "params": {"threshold": 0.6},
                        "weight": 0.5
                    },
                    {
                        "type": "topk",
                        "params": {"k": 5, "min_prob": 0.3},
                        "weight": 0.3
                    },
                    {
                        "type": "momentum",
                        "params": {"momentum_threshold": 0.2},
                        "weight": 0.2
                    }
                ]
            }
        }
    }

class CandlestickLLMBacktester:
    """K线LLM模型回测器"""
    def __init__(self, model, tokenizer, initial_capital=10000.0, device=None, signal_type=None, leverage=1.0):
        """
        初始化回测器

        Args:
            model: CandlestickLLM或CandlestickGPT4模型
            tokenizer: CandlestickTokenizer实例
            initial_capital: 初始资金
            device: 计算设备 (None表示自动选择)
            signal_type: 信号生成器类型 (None表示使用传统策略)
            leverage: 杠杆倍数，默认为1.0（无杠杆）
        """
        self.model = model
        self.tokenizer = tokenizer
        self.initial_capital = initial_capital
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self.leverage = leverage  # 添加杠杆倍数属性

        # 检测模型类型
        self.model_type = self._detect_model_type(model)
        print(f"检测到模型类型: {self.model_type}")

        if signal_type is None:
            signal_config = None
        else:
            signal_config = signal_configs[signal_type]
            print(f"使用信号生成器配置: {signal_config}")

        # 处理信号生成器
        if signal_config is not None and SignalGenerator is not None:
            # 根据配置创建信号生成器
            self.signal_generator = self._create_signal_generator(signal_config)
        else:
            # 不使用信号生成器
            self.signal_generator = None

    def _detect_model_type(self, model):
        """
        检测模型类型

        Args:
            model: 模型实例

        Returns:
            模型类型字符串: 'CandlestickVQGPT', 'CandlestickGPT4' 或 'Unknown'
        """
        if MODELS_IMPORTED:
            if isinstance(model, CandlestickVQGPT):
                return 'CandlestickVQGPT'
            elif isinstance(model, CandlestickGPT4):
                return 'CandlestickGPT4'

        # 如果无法直接通过isinstance判断，尝试通过类名判断
        class_name = model.__class__.__name__
        if class_name == 'CandlestickVQGPT':
            return 'CandlestickVQGPT'
        elif class_name == 'CandlestickGPT4':
            return 'CandlestickGPT4'

        # 如果还是无法判断，尝试检查模型的方法和属性
        if hasattr(model, 'generate') and hasattr(model, 'rotary_emb'):
            return 'CandlestickGPT4'
        elif hasattr(model, 'generate') and not hasattr(model, 'rotary_emb'):
            return 'CandlestickVQGPT'

        return 'Unknown'

    def _create_signal_generator(self, config):
        """
        根据配置创建信号生成器

        Args:
            config: 信号生成器配置
                格式: {
                    'type': 'threshold'|'topk'|'momentum'|'ensemble',
                    'params': {参数字典}
                }

        Returns:
            信号生成器实例
        """
        strategy_type = config.get('type', 'threshold')
        params = config.get('params', {})

        if strategy_type == 'threshold':
            threshold = params.get('threshold', 0.6)
            strategy = ThresholdDirectionStrategy(threshold=threshold)
        elif strategy_type == 'topk':
            k = params.get('k', 5)
            min_prob = params.get('min_prob', 0.3)
            strategy = TopKStrategy(k=k, min_prob=min_prob)
        elif strategy_type == 'momentum':
            momentum_threshold = params.get('momentum_threshold', 0.2)
            min_prob = params.get('min_prob', 0.3)
            strategy = StatisticalMomentumStrategy(momentum_threshold=momentum_threshold, min_prob=min_prob)
        elif strategy_type == 'ensemble':
            # 创建集成策略
            strategies_config = params.get('strategies', [])
            strategies = []

            for strat_config in strategies_config:
                sub_type = strat_config.get('type', 'threshold')
                sub_params = strat_config.get('params', {})
                weight = strat_config.get('weight', 1.0)

                # 递归创建子策略
                sub_config = {'type': sub_type, 'params': sub_params}
                sub_generator = self._create_signal_generator(sub_config)
                strategies.append((sub_generator.strategy, weight))

            # 如果没有子策略，使用默认策略
            if not strategies:
                strategies = [
                    (ThresholdDirectionStrategy(threshold=0.6), 0.5),
                    (TopKStrategy(k=5, min_prob=0.3), 0.3),
                    (StatisticalMomentumStrategy(momentum_threshold=0.2), 0.2)
                ]

            strategy = WeightedEnsembleStrategy(strategies)
        else:
            # 默认使用阈值策略
            strategy = ThresholdDirectionStrategy(threshold=0.6)

        return SignalGenerator(strategy)

    def backtest(self, df, code_id=0, seq_len=30, commission=0.001, threshold=0.6,
                stop_loss=None, take_profit=None, time_features=None, temperature=1.0,
                top_k=None, top_p=None, print_interval=10, leverage=None):
        """
        回测模型

        Args:
            df: 包含OHLCV数据的DataFrame
            code_id: 证券代码ID
            seq_len: 序列长度
            commission: 交易手续费率
            threshold: 交易信号阈值 (用于创建默认信号生成器)
            stop_loss: 止损比例 (None表示不使用止损)
            take_profit: 止盈比例 (None表示不使用止盈)
            time_features: 时间特征 (可选)
            temperature: 温度参数，控制预测的随机性 (1.0表示正常温度，<1.0更确定，>1.0更随机)
            top_k: Top-K采样参数，限制候选token数量 (None表示不使用)
            top_p: Nucleus采样参数，保留累积概率质量 (None表示不使用)
            print_interval: 打印详细信息的间隔步数
            leverage: 杠杆倍数，如果为None则使用初始化时设置的杠杆倍数

        Returns:
            包含回测结果的字典
        """
        # 确保模型处于评估模式
        self.model.eval()
        self.model.to(self.device)

        # 设置杠杆倍数
        current_leverage = leverage if leverage is not None else self.leverage
        print(f"使用杠杆倍数: {current_leverage}倍")

        # 初始化回测状态
        capital = self.initial_capital
        position = 0
        entry_price = 0
        trades = []
        equity_curve = [capital]
        daily_returns = []
        positions_history = []
        signals = []

        # 记录保证金和可用资金
        margin = 0  # 保证金
        available_capital = capital  # 可用资金


        csds = CandlestickDataset(
            data=[df],
            code_ids=[code_id],
            tokenizer=self.tokenizer,
            seq_len=seq_len,
            stride=1,
            # use_time_features=True
        )
        # 准备回测数据
        total_steps = len(df) - seq_len
        df =self.tokenizer._preprocess_df(df)
        print(f"开始回测，共 {len(csds)} 个时间步...")

        # # 计算ATR
        # atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=100)
        # atr = atr.bfill()  # 填充开始的NaN值
        # # 确保ATR不为零
        # min_atr = df['close'].mean() * 0.001  # 使用收盘价平均值的0.1%作为最小ATR
        # atr = atr.clip(lower=min_atr)
        # df['atr'] = atr
        # # 应用ATR乘数和缩放因子
        # df['atr'] = df['atr'] * 0.88 / 10

        # for i in range(seq_len, len(df)):
        for i, sample in enumerate(csds):
            # 计算并显示进度
            current_step = i
            if current_step % max(1, total_steps // 20) == 0 or current_step == total_steps:
                progress = current_step / total_steps * 100
                print(f"回测进度: {current_step}/{total_steps} ({progress:.1f}%)")
            # 获取当前K线
            current_candle = df.iloc[seq_len + i]
            current_price = current_candle['close']
            current_datetime = current_candle['datetime'] if 'datetime' in current_candle else i

            # 生成预测
            # 使用推荐的方式从张量创建新张量
            if isinstance(sample[0], torch.Tensor):
                input_tokens = sample[0].clone().detach().to(torch.int32).unsqueeze(0).to(self.device)
            else:
                input_tokens = torch.tensor(sample[0], dtype=torch.int32).unsqueeze(0).to(self.device)

            code_tensor = torch.tensor([sample[2]], dtype=torch.int32).to(self.device)

            if isinstance(sample[3], torch.Tensor):
                time_features = sample[3].clone().detach().to(torch.float32).unsqueeze(0).to(self.device)
            else:
                time_features = torch.tensor(sample[3], dtype=torch.float32).unsqueeze(0).to(self.device)
            with torch.no_grad():
                # 根据模型类型调用不同的前向传播方法
                if self.model_type == 'CandlestickGPT4':
                    # CandlestickGPT4模型的前向传播
                    logits, _ = self.model(input_tokens, code_tensor, time_features=time_features)
                else:
                    # CandlestickLLM模型的前向传播
                    # print(input_tokens)
                    # print(code_tensor)
                    # print(time_features)
                    logits, _ = self.model(input_tokens, code_tensor, time_features=time_features)
                    # print(logits.shape)

            # 获取最后一个时间步的logits
            last_logits = logits[0, -1]

            # 应用温度缩放
            if temperature != 1.0:
                last_logits = last_logits / temperature

            # 应用top-k采样
            if top_k is not None and top_k > 0:
                v, _ = torch.topk(last_logits, min(top_k, last_logits.size(-1)))
                last_logits[last_logits < v[-1]] = -float('Inf')

            # 应用nucleus (top-p) 采样
            if top_p is not None and 0 < top_p < 1:
                sorted_logits, sorted_indices = torch.sort(last_logits, descending=True)
                cumulative_probs = torch.cumsum(torch.softmax(sorted_logits, dim=-1), dim=-1)

                # 找到累积概率超过top_p的位置
                sorted_indices_to_remove = cumulative_probs > top_p
                # 保留第一个超过top_p的token
                sorted_indices_to_remove[1:] = sorted_indices_to_remove[:-1].clone()
                sorted_indices_to_remove[0] = 0

                # 将要移除的token的logits设为负无穷
                indices_to_remove = sorted_indices[sorted_indices_to_remove]
                last_logits[indices_to_remove] = -float('Inf')

            """
            # 添加随机噪声以增加多样性
            if temperature > 0:
                # 生成随机噪声，噪声大小与温度成正比
                noise = torch.randn_like(last_logits) * temperature * 0.1
                last_logits = last_logits + noise
            # 应用温度参数调整logits
            if temperature != 1.0:
                last_logits = last_logits / max(0.1, temperature)
            # 计算概率分布
            probs = F.softmax(last_logits, dim=-1).cpu().numpy()
            # 只在特定间隔或开始/结束时打印详细信息
            should_print = (current_step % print_interval == 0) or (current_step == 1) or (current_step == total_steps)
            if should_print:
                # 获取概率最高的前5个token用于打印
                top_indices = np.argsort(probs)[-5:]
                print("Top 5 tokens:", [ self.tokenizer.idx2token[idx] for idx in top_indices ])
                print("Top 5 probs:", [ probs[idx] for idx in top_indices ])
            """
            # 这里不再需要记录信号概率，因为信号生成器会处理

            # 检查止损和止盈
            if position != 0 and (stop_loss is not None or take_profit is not None):
                if position > 0:  # 多头
                    # 计算当前收益率（考虑杠杆）
                    unrealized_profit = (current_price - entry_price) * position
                    current_return = unrealized_profit / margin  # 相对于保证金的收益率

                    # 止损
                    if stop_loss is not None and current_return < -stop_loss:
                        # 平多，计算收益
                        profit = unrealized_profit - commission * (entry_price + current_price) * position
                        # 更新资金
                        capital = margin + available_capital + profit
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'STOP_LOSS',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'margin': 0,
                            'available_capital': capital,
                            'equity': capital,
                            'profit': profit,
                            'return_rate': profit / margin  # 保证金收益率
                        })
                        position = 0
                        margin = 0
                        available_capital = capital

                    # 止盈
                    elif take_profit is not None and current_return > take_profit:
                        # 平多，计算收益
                        profit = unrealized_profit - commission * (entry_price + current_price) * position
                        # 更新资金
                        capital = margin + available_capital + profit
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'TAKE_PROFIT',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'margin': 0,
                            'available_capital': capital,
                            'equity': capital,
                            'profit': profit,
                            'return_rate': profit / margin  # 保证金收益率
                        })
                        position = 0
                        margin = 0
                        available_capital = capital

                elif position < 0:  # 空头
                    # 计算当前收益率（考虑杠杆）
                    unrealized_profit = (entry_price - current_price) * (-position)
                    current_return = unrealized_profit / margin  # 相对于保证金的收益率

                    # 止损
                    if stop_loss is not None and current_return < -stop_loss:
                        # 平空，计算收益
                        profit = unrealized_profit - commission * (entry_price + current_price) * (-position)
                        # 更新资金
                        capital = margin + available_capital + profit
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'STOP_LOSS',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'margin': 0,
                            'available_capital': capital,
                            'equity': capital,
                            'profit': profit,
                            'return_rate': profit / margin  # 保证金收益率
                        })
                        position = 0
                        margin = 0
                        available_capital = capital

                    # 止盈
                    elif take_profit is not None and current_return > take_profit:
                        # 平空，计算收益
                        profit = unrealized_profit - commission * (entry_price + current_price) * (-position)
                        # 更新资金
                        capital = margin + available_capital + profit
                        trades.append({
                            'datetime': current_datetime,
                            'action': 'TAKE_PROFIT',
                            'price': current_price,
                            'position': 0,
                            'capital': capital,
                            'margin': 0,
                            'available_capital': capital,
                            'equity': capital,
                            'profit': profit,
                            'return_rate': profit / margin  # 保证金收益率
                        })
                        position = 0
                        margin = 0
                        available_capital = capital

            # 生成交易信号
            # 如果没有提供信号生成器，则创建一个默认的
            if self.signal_generator is None and SignalGenerator is not None:
                from pyqlab.models.gpt2.signal_generator import ThresholdDirectionStrategy
                default_strategy = ThresholdDirectionStrategy(threshold=threshold)
                self.signal_generator = SignalGenerator(default_strategy)

            # 使用信号生成器生成信号
            if self.signal_generator is not None:
                # 重新构造logits张量（保持原始形状但使用修改后的最后一个时间步）
                modified_logits = logits.clone()
                modified_logits[0, -1] = last_logits

                signal_info = self.signal_generator.generate_signal(
                    logits=modified_logits,
                    tokenizer=self.tokenizer,
                    temperature=1.0,  # 温度已经应用过了，这里使用1.0
                    prev_close=df.iloc[i]['Prev_Close'],
                    ma_volume=df.iloc[i]['MA_Volume'],
                    atr=df.iloc[i]['ATR']
                )

                # 获取信号
                signal = signal_info['signal']
                print(f"生成信号: {signal}")

                # 根据当前持仓状态调整信号
                if position > 0 and signal == 'SELL':  # 当前持有多头，收到卖出信号
                    signal = 'CLOSE'
                elif position < 0 and signal == 'BUY':  # 当前持有空头，收到买入信号
                    signal = 'CLOSE'

                # 记录更详细的信号信息
                signal_record = {
                    'datetime': current_datetime,
                    'price': current_price,
                    'signal': signal,
                    'confidence': signal_info.get('confidence', 0.0)
                }

                # 添加其他可能的信息
                if 'direction_probs' in signal_info:
                    signal_record['up_prob'] = signal_info['direction_probs'].get('up', 0.0)
                    signal_record['down_prob'] = signal_info['direction_probs'].get('down', 0.0)
                    signal_record['flat_prob'] = signal_info['direction_probs'].get('flat', 0.0)

                signals.append(signal_record)
            else:
                # 如果无法创建信号生成器，使用一个默认的HOLD信号
                signal = 'HOLD'
                signals.append({
                    'datetime': current_datetime,
                    'price': current_price,
                    'signal': signal
                })

            # 执行交易
            if signal == 'BUY' and position == 0:
                # 计算杠杆后的持仓量
                position_value = capital * current_leverage  # 杠杆后的持仓价值
                position = position_value / current_price  # 持仓数量

                # 计算所需保证金
                margin = position_value / current_leverage  # 保证金 = 持仓价值 / 杠杆倍数
                available_capital = capital - margin  # 可用资金 = 总资金 - 保证金

                entry_price = current_price
                trades.append({
                    'datetime': current_datetime,
                    'action': 'BUY',
                    'price': current_price,
                    'position': position,
                    'margin': margin,
                    'available_capital': available_capital,
                    'leverage': current_leverage,
                    'equity': position * current_price
                })
            elif signal == 'SELL' and position == 0:
                # 计算杠杆后的持仓量
                position_value = capital * current_leverage  # 杠杆后的持仓价值
                position = -position_value / current_price  # 负持仓数量表示空头

                # 计算所需保证金
                margin = position_value / current_leverage  # 保证金 = 持仓价值 / 杠杆倍数
                available_capital = capital - margin  # 可用资金 = 总资金 - 保证金

                entry_price = current_price
                trades.append({
                    'datetime': current_datetime,
                    'action': 'SELL',
                    'price': current_price,
                    'position': position,
                    'margin': margin,
                    'available_capital': available_capital,
                    'leverage': current_leverage,
                    'equity': -position * current_price
                })
            elif signal == 'CLOSE' and position != 0:
                # 平仓
                if position > 0:
                    # 平多，计算收益
                    profit = (current_price - entry_price) * position - commission * (entry_price + current_price) * position
                    # 更新资金
                    capital = margin + available_capital + profit
                    trades.append({
                        'datetime': current_datetime,
                        'action': 'CLOSE_LONG',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'margin': 0,
                        'available_capital': capital,
                        'equity': capital,
                        'profit': profit,
                        'return_rate': profit / margin  # 保证金收益率
                    })
                else:
                    # 平空，计算收益
                    profit = (entry_price - current_price) * (-position) - commission * (entry_price + current_price) * (-position)
                    # 更新资金
                    capital = margin + available_capital + profit
                    trades.append({
                        'datetime': current_datetime,
                        'action': 'CLOSE_SHORT',
                        'price': current_price,
                        'position': 0,
                        'capital': capital,
                        'margin': 0,
                        'available_capital': capital,
                        'equity': capital,
                        'profit': profit,
                        'return_rate': profit / margin  # 保证金收益率
                    })
                # 重置持仓和保证金
                position = 0
                margin = 0
                available_capital = capital

            # 更新权益曲线
            if position == 0:
                equity = capital
            elif position > 0:
                # 多头权益 = 保证金 + 可用资金 + 未实现盈亏
                unrealized_profit = (current_price - entry_price) * position
                equity = margin + available_capital + unrealized_profit
            else:
                # 空头权益 = 保证金 + 可用资金 + 未实现盈亏
                unrealized_profit = (entry_price - current_price) * (-position)
                equity = margin + available_capital + unrealized_profit

            equity_curve.append(equity)

            # 记录持仓历史
            positions_history.append({
                'datetime': current_datetime,
                'position': position,
                'equity': equity,
                'margin': margin,
                'available_capital': available_capital,
                'leverage': current_leverage if position != 0 else 1.0
            })

            # 计算每日收益率
            if i > seq_len:
                daily_return = (equity_curve[-1] / equity_curve[-2]) - 1
                daily_returns.append(daily_return)

        # 计算回测指标
        returns = np.array(daily_returns)
        total_return = equity_curve[-1] / self.initial_capital - 1
        annual_return = (1 + total_return) ** (252 / len(df)) - 1

        # 计算夏普比率
        sharpe_ratio = 0
        if len(returns) > 0 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)

        # 计算最大回撤
        max_drawdown = 0
        peak = equity_curve[0]

        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)

        # 计算胜率
        win_trades = [t for t in trades if 'profit' in t and t['profit'] > 0]
        win_rate = len(win_trades) / len(trades) if trades else 0

        # 计算盈亏比
        avg_win = np.mean([t['profit'] for t in win_trades]) if win_trades else 0
        lose_trades = [t for t in trades if 'profit' in t and t['profit'] <= 0]
        avg_loss = np.mean([t['profit'] for t in lose_trades]) if lose_trades else 0
        profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

        # 汇总结果
        results = {
            'initial_capital': self.initial_capital,
            'final_equity': equity_curve[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio,
            'leverage': current_leverage,  # 添加杠杆信息
            'trades': trades,
            'equity_curve': equity_curve,
            'positions_history': positions_history,
            'signals': signals
        }

        # 打印回测完成信息和关键指标
        print("\n回测完成！")
        print(f"初始资金: ${self.initial_capital:.2f}")
        print(f"最终权益: ${equity_curve[-1]:.2f}")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"胜率: {win_rate:.2%}")
        print(f"盈亏比: {profit_loss_ratio:.2f}")
        print(f"杠杆倍数: {current_leverage:.1f}倍")
        print(f"交易次数: {len(trades)}")

        if len(trades) > 0:
            print("\n最近5笔交易:")
            for trade in trades[-5:]:
                action = trade['action']
                price = trade['price']
                profit = trade.get('profit', 'N/A')
                if isinstance(profit, (int, float)):
                    profit_str = f"{profit:.2f}"
                else:
                    profit_str = str(profit)
                print(f"  {action} @ {price:.2f} (利润: {profit_str})")

        return results

    def visualize_backtest(self, df, results, seq_len=30, save_path=None):
        """
        可视化回测结果

        Args:
            df: 包含OHLCV数据的DataFrame
            results: 回测结果字典
            seq_len: 序列长度
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 15),
                                          gridspec_kw={'height_ratios': [3, 1, 1]})

        # 处理5分钟K线图时间轴不连续的问题
        # 创建df的副本以避免SettingWithCopyWarning
        df = df.copy()
        if 'datetime' in df.columns:
            # 确保datetime列是datetime类型
            df['datetime'] = pd.to_datetime(df['datetime'])

            # 创建交易时间索引
            # 方法1: 使用序号索引而不是实际时间
            x_index = np.arange(len(df))

            # 创建时间标签映射，用于显示
            time_labels = df['datetime'].dt.strftime('%Y-%m-%d %H:%M').values

            # 设置主要刻度位置
            tick_positions = []
            tick_labels = []

            # 找出每天的第一个数据点作为主要刻度
            current_date = None
            for i, dt in enumerate(df['datetime']):
                date = dt.date()
                if date != current_date:
                    current_date = date
                    tick_positions.append(i)
                    tick_labels.append(dt.strftime('%Y-%m-%d'))
        else:
            x_index = range(len(df))
            time_labels = [str(i) for i in x_index]
            tick_positions = list(range(0, len(df), max(1, len(df) // 10)))
            tick_labels = [str(pos) for pos in tick_positions]

        # 绘制K线图
        for i in range(len(df)):
            open_price = df['open'].iloc[i]
            close_price = df['close'].iloc[i]
            high_price = df['high'].iloc[i]
            low_price = df['low'].iloc[i]
            color = 'red' if close_price >= open_price else 'green'

            # 使用索引位置绘制K线
            ax1.plot([x_index[i], x_index[i]], [open_price, close_price], color=color, linewidth=2)
            ax1.plot([x_index[i], x_index[i]], [low_price, high_price], color=color, linewidth=1)

        # 标记交易点
        for trade in results['trades']:
            # 找到交易时间对应的索引位置
            if 'datetime' in df.columns and isinstance(trade['datetime'], (pd.Timestamp, str)):
                trade_time = pd.to_datetime(trade['datetime'])
                # 找到最接近的时间点
                time_diff = abs(df['datetime'] - trade_time)
                closest_idx = time_diff.argmin()
                x_pos = x_index[closest_idx]
            elif isinstance(trade['datetime'], int) and trade['datetime'] < len(df):
                x_pos = x_index[trade['datetime']]
            else:
                # 如果无法确定位置，跳过
                continue

            if trade['action'] == 'BUY':
                ax1.scatter(x_pos, trade['price'], marker='^', color='blue', s=100)
                ax1.annotate('Buy', (x_pos, trade['price']), xytext=(0, 10),
                           textcoords='offset points', ha='center')
            elif trade['action'] == 'SELL':
                ax1.scatter(x_pos, trade['price'], marker='v', color='purple', s=100)
                ax1.annotate('Sell', (x_pos, trade['price']), xytext=(0, -20),
                           textcoords='offset points', ha='center')
            elif trade['action'] in ['CLOSE_LONG', 'TAKE_PROFIT', 'STOP_LOSS'] and trade.get('profit', 0) > 0:
                ax1.scatter(x_pos, trade['price'], marker='o', color='blue', s=100)
                ax1.annotate(f"{trade['action']} +{trade.get('profit', 0):.2f}",
                           (x_pos, trade['price']), xytext=(0, 10),
                           textcoords='offset points', ha='center')
            elif trade['action'] in ['CLOSE_SHORT', 'TAKE_PROFIT', 'STOP_LOSS'] and trade.get('profit', 0) > 0:
                ax1.scatter(x_pos, trade['price'], marker='o', color='purple', s=100)
                ax1.annotate(f"{trade['action']} +{trade.get('profit', 0):.2f}",
                           (x_pos, trade['price']), xytext=(0, -20),
                           textcoords='offset points', ha='center')
            elif trade['action'] in ['CLOSE_LONG', 'TAKE_PROFIT', 'STOP_LOSS']:
                ax1.scatter(x_pos, trade['price'], marker='o', color='red', s=100)
                ax1.annotate(f"{trade['action']} {trade.get('profit', 0):.2f}",
                           (x_pos, trade['price']), xytext=(0, 10),
                           textcoords='offset points', ha='center')
            elif trade['action'] in ['CLOSE_SHORT', 'TAKE_PROFIT', 'STOP_LOSS']:
                ax1.scatter(x_pos, trade['price'], marker='o', color='red', s=100)
                ax1.annotate(f"{trade['action']} {trade.get('profit', 0):.2f}",
                           (x_pos, trade['price']), xytext=(0, -20),
                           textcoords='offset points', ha='center')

        # 设置K线图标题和标签
        ax1.set_title('K线图与交易信号')
        ax1.set_ylabel('价格')

        # 设置x轴刻度和标签
        ax1.set_xticks(tick_positions)
        ax1.set_xticklabels(tick_labels, rotation=45)

        # 添加网格线
        ax1.grid(True, linestyle='--', alpha=0.6)

        # 绘制权益曲线
        # 创建权益曲线的x轴索引
        if len(results['equity_curve']) > 0:
            # 确保equity_curve的长度与数据点数量匹配
            equity_indices = np.linspace(0, len(x_index) - 1, len(results['equity_curve'])).astype(int)
            # 使用这些索引获取对应的x轴位置
            equity_x = [x_index[min(idx, len(x_index) - 1)] for idx in equity_indices]

            ax2.plot(equity_x, results['equity_curve'], color='blue', linewidth=2)
            ax2.set_title('权益曲线')
            ax2.set_ylabel('权益')

            # 设置与K线图相同的x轴刻度
            ax2.set_xticks(tick_positions)
            ax2.set_xticklabels(tick_labels, rotation=45)
            ax2.grid(True, linestyle='--', alpha=0.6)

        # 绘制持仓量
        if 'positions_history' in results and len(results['positions_history']) > 0:
            positions = [p['position'] for p in results['positions_history']]

            # 为持仓历史创建x轴索引
            if 'datetime' in df.columns:
                pos_indices = []
                for p in results['positions_history']:
                    if isinstance(p['datetime'], (str, pd.Timestamp)):
                        pos_time = pd.to_datetime(p['datetime'])
                        # 找到最接近的时间点
                        time_diff = abs(df['datetime'] - pos_time)
                        closest_idx = time_diff.argmin()
                        pos_indices.append(closest_idx)
                    elif isinstance(p['datetime'], int) and p['datetime'] < len(df):
                        pos_indices.append(p['datetime'])
                    else:
                        # 如果无法确定位置，使用0
                        pos_indices.append(0)

                # 使用索引获取x轴位置
                pos_x = [x_index[min(idx, len(x_index) - 1)] for idx in pos_indices]
            else:
                # 如果没有datetime列，直接使用索引
                pos_x = [x_index[min(p['datetime'], len(x_index) - 1)] if isinstance(p['datetime'], int) else 0
                         for p in results['positions_history']]

            ax3.plot(pos_x, positions, color='green', linewidth=2)
            ax3.set_title('持仓量')
            ax3.set_ylabel('持仓')

            # 设置与K线图相同的x轴刻度
            ax3.set_xticks(tick_positions)
            ax3.set_xticklabels(tick_labels, rotation=45)

            # 添加零线
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax3.grid(True, linestyle='--', alpha=0.6)

        # 添加回测指标
        textstr = '\n'.join((
            f'初始资金: ${results["initial_capital"]:.2f}',
            f'最终权益: ${results["final_equity"]:.2f}',
            f'总收益率: {results["total_return"]:.2%}',
            f'年化收益率: {results["annual_return"]:.2%}',
            f'夏普比率: {results["sharpe_ratio"]:.2f}',
            f'最大回撤: {results["max_drawdown"]:.2%}',
            f'胜率: {results["win_rate"]:.2%}',
            f'盈亏比: {results["profit_loss_ratio"]:.2f}',
            f'杠杆倍数: {results.get("leverage", 1.0):.1f}倍',
            f'交易次数: {len(results["trades"])}'
        ))

        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax2.text(0.05, 0.95, textstr, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', bbox=props)

        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

        return fig

    def save_results(self, results, save_path):
        """
        保存回测结果到JSON文件

        Args:
            results: 回测结果字典
            save_path: 保存路径
        """
        # 创建可序列化的结果副本
        serializable_results = results.copy()

        # 处理不可序列化的对象
        if 'equity_curve' in serializable_results:
            serializable_results['equity_curve'] = [float(x) for x in serializable_results['equity_curve']]

        # 处理datetime对象
        for trade in serializable_results.get('trades', []):
            if 'datetime' in trade and isinstance(trade['datetime'], (pd.Timestamp, datetime)):
                trade['datetime'] = trade['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        for pos in serializable_results.get('positions_history', []):
            if 'datetime' in pos and isinstance(pos['datetime'], (pd.Timestamp, datetime)):
                pos['datetime'] = pos['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        for signal in serializable_results.get('signals', []):
            if 'datetime' in signal and isinstance(signal['datetime'], (pd.Timestamp, datetime)):
                signal['datetime'] = signal['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        # 保存到文件
        with open(save_path, 'w') as f:
            json.dump(serializable_results, f, indent=4)

        print(f"回测结果已保存到 {save_path}")

    def load_results(self, load_path):
        """
        从JSON文件加载回测结果

        Args:
            load_path: 加载路径

        Returns:
            回测结果字典
        """
        with open(load_path, 'r') as f:
            results = json.load(f)

        # 处理datetime对象
        for trade in results.get('trades', []):
            if 'datetime' in trade and isinstance(trade['datetime'], str):
                try:
                    trade['datetime'] = pd.to_datetime(trade['datetime'])
                except:
                    pass

        for pos in results.get('positions_history', []):
            if 'datetime' in pos and isinstance(pos['datetime'], str):
                try:
                    pos['datetime'] = pd.to_datetime(pos['datetime'])
                except:
                    pass

        for signal in results.get('signals', []):
            if 'datetime' in signal and isinstance(signal['datetime'], str):
                try:
                    signal['datetime'] = pd.to_datetime(signal['datetime'])
                except:
                    pass

        return results

    def compare_strategies(self, results_list, names=None, save_path=None):
        """
        比较多个回测策略的结果

        Args:
            results_list: 回测结果字典列表
            names: 策略名称列表 (可选)
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        if names is None:
            names = [f"策略 {i+1}" for i in range(len(results_list))]

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 绘制权益曲线
        for i, results in enumerate(results_list):
            ax1.plot(range(len(results['equity_curve'])), results['equity_curve'],
                    label=names[i], linewidth=2)

        ax1.set_title('权益曲线比较')
        ax1.set_ylabel('权益')
        ax1.legend()
        ax1.grid(True)

        # 创建性能指标表格
        metrics = ['total_return', 'annual_return', 'sharpe_ratio', 'max_drawdown',
                  'win_rate', 'profit_loss_ratio', 'leverage']
        metric_names = ['总收益率', '年化收益率', '夏普比率', '最大回撤', '胜率', '盈亏比', '杠杆倍数']

        data = []
        for i, results in enumerate(results_list):
            row = [names[i]]
            for metric in metrics:
                if metric in ['total_return', 'annual_return', 'max_drawdown', 'win_rate']:
                    row.append(f"{results.get(metric, 0):.2%}")
                else:
                    row.append(f"{results.get(metric, 0):.2f}")
            data.append(row)

        ax2.axis('tight')
        ax2.axis('off')
        table = ax2.table(cellText=data,
                         colLabels=['策略'] + metric_names,
                         loc='center', cellLoc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 1.5)

        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

        return fig



    def save_results(self, results, save_path):
        """
        保存回测结果到JSON文件

        Args:
            results: 回测结果字典
            save_path: 保存路径
        """
        # 创建可序列化的结果副本
        serializable_results = results.copy()

        # 处理不可序列化的对象
        if 'equity_curve' in serializable_results:
            serializable_results['equity_curve'] = [float(x) for x in serializable_results['equity_curve']]

        # 处理datetime对象
        for trade in serializable_results.get('trades', []):
            if 'datetime' in trade and isinstance(trade['datetime'], (pd.Timestamp, datetime)):
                trade['datetime'] = trade['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        for pos in serializable_results.get('positions_history', []):
            if 'datetime' in pos and isinstance(pos['datetime'], (pd.Timestamp, datetime)):
                pos['datetime'] = pos['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        for signal in serializable_results.get('signals', []):
            if 'datetime' in signal and isinstance(signal['datetime'], (pd.Timestamp, datetime)):
                signal['datetime'] = signal['datetime'].strftime('%Y-%m-%d %H:%M:%S')

        # 保存到文件
        with open(save_path, 'w') as f:
            json.dump(serializable_results, f, indent=4)

        print(f"回测结果已保存到 {save_path}")

    def load_results(self, load_path):
        """
        从JSON文件加载回测结果

        Args:
            load_path: 加载路径

        Returns:
            回测结果字典
        """
        with open(load_path, 'r') as f:
            results = json.load(f)

        # 处理datetime对象
        for trade in results.get('trades', []):
            if 'datetime' in trade and isinstance(trade['datetime'], str):
                try:
                    trade['datetime'] = pd.to_datetime(trade['datetime'])
                except:
                    pass

        for pos in results.get('positions_history', []):
            if 'datetime' in pos and isinstance(pos['datetime'], str):
                try:
                    pos['datetime'] = pd.to_datetime(pos['datetime'])
                except:
                    pass

        for signal in results.get('signals', []):
            if 'datetime' in signal and isinstance(signal['datetime'], str):
                try:
                    signal['datetime'] = pd.to_datetime(signal['datetime'])
                except:
                    pass

        return results

    def compare_strategies(self, results_list, names=None, save_path=None):
        """
        比较多个回测策略的结果

        Args:
            results_list: 回测结果字典列表
            names: 策略名称列表 (可选)
            save_path: 保存图表的路径 (可选)

        Returns:
            matplotlib图表对象
        """
        if names is None:
            names = [f"策略 {i+1}" for i in range(len(results_list))]

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # 绘制权益曲线
        for i, results in enumerate(results_list):
            ax1.plot(range(len(results['equity_curve'])), results['equity_curve'],
                    label=names[i], linewidth=2)

        ax1.set_title('权益曲线比较')
        ax1.set_ylabel('权益')
        ax1.legend()
        ax1.grid(True)

        # 创建性能指标表格
        metrics = ['total_return', 'annual_return', 'sharpe_ratio', 'max_drawdown',
                  'win_rate', 'profit_loss_ratio', 'leverage']
        metric_names = ['总收益率', '年化收益率', '夏普比率', '最大回撤', '胜率', '盈亏比', '杠杆倍数']

        data = []
        for i, results in enumerate(results_list):
            row = [names[i]]
            for metric in metrics:
                if metric in ['total_return', 'annual_return', 'max_drawdown', 'win_rate']:
                    row.append(f"{results.get(metric, 0):.2%}")
                else:
                    row.append(f"{results.get(metric, 0):.2f}")
            data.append(row)

        ax2.axis('tight')
        ax2.axis('off')
        table = ax2.table(cellText=data,
                         colLabels=['策略'] + metric_names,
                         loc='center', cellLoc='center')
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1, 1.5)

        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

        return fig
